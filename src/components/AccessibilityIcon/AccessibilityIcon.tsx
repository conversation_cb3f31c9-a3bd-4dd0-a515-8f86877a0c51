import { useIntl } from 'react-intl';

import { Text } from 'components/Typography';

import { messages } from './messages';
import { AccessibilityFeature, AccessibilityIconProps } from './types';

export const AccessibilityIcon = ({ type }: AccessibilityIconProps) => {
  const intl = useIntl();

  const messagesMap = {
    [AccessibilityFeature.AUDIO_DESCRIPTION]: messages.audioDescription,
    [AccessibilityFeature.SUBTITLES_FOR_DEAF]: messages.subtitlesForDeaf,
    [AccessibilityFeature.SIGN_LANGUAGE]: messages.signLanguage,
  };

  const message = messagesMap[type];

  return (
    <Text $sizeLarge title={intl.formatMessage(message.title)}>
      {intl.formatMessage(message)}
    </Text>
  );
};
