import { describe, it, expect } from 'vitest';
import { screen } from '@testing-library/react';
import { render } from 'utils/testing';

import { AccessibilityIcon } from '../AccessibilityIcon';
import { AccessibilityFeature } from '../types';

describe('Components:AccessibilityIcon', () => {
  it('renders audio description icon correctly', () => {
    render(<AccessibilityIcon type={AccessibilityFeature.AUDIO_DESCRIPTION} />);

    expect(screen.getByText('AD')).toBeInTheDocument();
    expect(screen.getByTitle('Audiodeskrypcja')).toBeInTheDocument();
  });

  it('renders subtitles for deaf icon correctly', () => {
    render(
      <AccessibilityIcon type={AccessibilityFeature.SUBTITLES_FOR_DEAF} />,
    );

    expect(screen.getByText('N')).toBeInTheDocument();
    expect(screen.getByTitle('Napisy dla ni<PERSON>łys<PERSON>ący<PERSON>')).toBeInTheDocument();
  });

  it('renders sign language icon correctly', () => {
    render(<AccessibilityIcon type={AccessibilityFeature.SIGN_LANGUAGE} />);

    expect(screen.getByText('JM')).toBeInTheDocument();
    expect(
      screen.getByTitle('Tłumaczenie na język migowy'),
    ).toBeInTheDocument();
  });
});
