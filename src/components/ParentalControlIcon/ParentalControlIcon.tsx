import { FC, PropsWithChildren } from 'react';

import * as S from './styles';
import { PR_IMAGES, PR_TITLES } from './constants';

export const ParentalControlIcon: FC<
  PropsWithChildren<{
    prLevel: number | string;
  }>
> = ({ prLevel }) => {
  return (
    <S.Image
      src={PR_IMAGES[prLevel]}
      title={PR_TITLES[prLevel]}
      alt={`parent-control-icon-${prLevel}`}
      height='3.2rem'
      width='3.2rem'
    />
  );
};
