import { useCallback, useRef } from 'react';

import { useErrorScreen } from 'services/error';
import { useLogger } from 'services/logger';
import { useGlobalLoaderContext } from 'services/loader/context';
import {
  usePlayerErrorScreen,
  usePlayerPlayback,
} from 'features/Player/Context';

import { PlayerErrorType } from './types';
import { DRM_CATEGORY_ERROR, NETWORK_CATEGORY_ERROR } from './constants';

let tryCount = 0;
const refreshTime = [1000, 3000, 5000];

export const usePlayerError = () => {
  const timeoutRef = useRef<any>(null);
  const { showErrorModal } = useErrorScreen();
  const { reset: resetPlayback } = usePlayerPlayback();
  const { logger } = useLogger();
  const { showErrorScreen } = usePlayerErrorScreen();
  const { setIsLoaderVisible } = useGlobalLoaderContext();

  const handleCDNError = useCallback(
    (reloadPlayer: () => void) => {
      clearTimeout(timeoutRef.current);

      if (tryCount < 3) {
        timeoutRef.current = setTimeout(() => {
          tryCount += 1;
          reloadPlayer();
        }, refreshTime[tryCount]);
      } else {
        showErrorScreen('CDN_ERROR', () => {
          reloadPlayer();
          tryCount = 0;
        });
      }
    },
    [showErrorScreen],
  );

  const handleExpiredError = useCallback(
    (reloadPlayer: () => void) => {
      showErrorScreen('EXPIRED_ERROR', reloadPlayer);
    },
    [showErrorScreen],
  );

  const handleHomeNetworkError = useCallback(() => {
    showErrorScreen('HOME_NETWORK_ERROR');
  }, [showErrorScreen]);

  const errorHandler = useCallback(
    (error: PlayerErrorType, reload: () => void) => {
      setIsLoaderVisible(false);
      const { message, code, category, data } = error;
      logger.error(
        `Player error: \n Category_Code: ${category}_${code} \n Message: ${message} \n Data: ${data}`,
      );
      switch (data.status) {
        case 451:
          resetPlayback();
          showErrorModal('BAD_COUNTRY_CODE');
          break;
        case 420:
          handleCDNError(reload);
          break;
        case 419:
          handleExpiredError(reload);
          break;
        case 403:
          handleHomeNetworkError();
          break;
        default:
          resetPlayback();
          if (
            category === NETWORK_CATEGORY_ERROR ||
            category === DRM_CATEGORY_ERROR
          ) {
            showErrorModal('PLAYER_CDN_ERROR', `Kod błędu: ${code}`);
          } else {
            showErrorModal('PLAYER_ERROR');
          }
          break;
      }
    },
    [
      handleCDNError,
      handleExpiredError,
      handleHomeNetworkError,
      logger,
      resetPlayback,
      setIsLoaderVisible,
      showErrorModal,
    ],
  );

  return {
    handleCDNError,
    errorHandler,
  };
};
