import styled from 'styled-components';

import { H2, Text } from 'components/Typography';
import { setSliderRecordingCardSizesVertical } from 'theme';

export const PosterContainer = styled.div`
  flex-grow: 0;
  flex-shrink: 0;
  margin-right: 1.6rem;

  ${setSliderRecordingCardSizesVertical('1.6rem')}
`;

export const DetailsWrapper = styled.div`
  height: 100%;
  overflow-y: auto;
  margin-right: -16px;
  padding-right: 16px;
`;

export const TitleStyled = styled(H2)`
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.8rem;
`;

export const DarkText = styled(Text)`
  display: block;
  color: ${({ theme }) => theme.colors.white75};
  margin-bottom: 0.4rem;
`;

export const Desc = styled(Text)`
  display: block;
  color: ${({ theme }) => theme.colors.white50};
  margin-bottom: 2.4rem;
  width: 60%;
`;

export const ActorContainer = styled.div`
  margin-top: 3.2rem;
`;

export const EpisodeWrapper = styled.div`
  margin-right: 2.4rem;
`;

export const OtherEpisodesContainer = styled.div`
  margin-top: 3.2rem;
`;

export const ActorsSliderWrapper = styled.div`
  margin-top: 1.2rem;
`;

export const OtherEpisodesSliderWrapper = styled.div`
  margin-top: 0.2rem;
`;

export const Spacer = styled.div`
  margin-bottom: 3.2rem;
`;
