import { FC, PropsWithChildren, useCallback } from 'react';
import { useIntl } from 'react-intl';
import { useTheme } from 'styled-components';

import { Image } from 'components/Image';
import { Text } from 'components/Typography';
import { Loader } from 'components/Loader';
import { PrimaryButton } from 'components/Buttons/PrimaryButton';
import { ModalConfirmation } from 'components/ModalConfirmation';
import { IconWarning } from 'components/Icons';
import { ParentalControlIcon } from 'components/ParentalControlIcon';
import { isExpiredDateFromString } from 'utils/dateUtils';
import { getPublicAssetUrl } from 'utils/url';
import { getAccessibilityFeaturesList } from 'utils/misc';
import { AccessibilityIcon } from 'components/AccessibilityIcon';

import { DetailsRecordingProps } from './types';
import { useDetailsRecording } from './hooks';
import * as S from './styles';
import { messages } from './messages';

import { useRecordingChannelFeatures } from '../useRecordingChannelFeatures';

export const DetailsRecording: FC<PropsWithChildren<DetailsRecordingProps>> = ({
  dataDetailsView,
  setBackgroundImage,
  setData,
}) => {
  const appTheme = useTheme();
  const { formatMessage } = useIntl();
  const {
    image,
    title,
    prLevel,
    properties,
    isRecording,
    shortInfo,
    description,
    isFetching,
    isBeforeDelete,
    isFailed,
    recordingchannelExtId,
    onPlayClick,
    handleDeleteButton,
    handleDeniedButton,
    handleDeleteConfirmed,
  } = useDetailsRecording({
    dataDetailsView,
    setBackgroundImage,
    setData,
  });

  const { isNpvrForRecordingChannelDisabled } = useRecordingChannelFeatures({
    recordingchannelExtId: recordingchannelExtId,
  });

  const renderShortInfo = useCallback(() => {
    return shortInfo?.map(
      ({ title: shortInfoTitle, info }) =>
        Boolean(info) && (
          <p key={shortInfoTitle}>
            <Text $highlight $sizeSmall>
              {shortInfoTitle}:
            </Text>{' '}
            <Text $sizeSmall>{info}</Text>
          </p>
        ),
    );
  }, [shortInfo]);

  const accessibilityIconsRender = () => {
    const accessibilityFeatures = getAccessibilityFeaturesList(properties);
    return accessibilityFeatures.map((featureType) => (
      <AccessibilityIcon type={featureType} />
    ));
  };

  const shouldShowPlayButton =
    !isExpiredDateFromString(dataDetailsView.expirationDate) &&
    !isFailed &&
    !isNpvrForRecordingChannelDisabled;

  return (
    <>
      {isFetching ? (
        <Loader />
      ) : (
        <>
          {isBeforeDelete && (
            <ModalConfirmation
              modalTitle={formatMessage(
                isRecording ? messages.cancelMessage : messages.deleteMessage,
              )}
              isOpen={isBeforeDelete}
              onClose={handleDeniedButton}
              onDenied={handleDeniedButton}
              onSubmit={handleDeleteConfirmed}
              buttonSubmitText={formatMessage(messages.deleteConfirmation)}
              buttonDeniedText={formatMessage(messages.deleteDenied)}
            />
          )}
          <S.RecordingDetailsWrapper data-testid='DetailsRecording-RecordingDetails'>
            <S.TopGrid>
              <S.ImageWrapper>
                {isFailed && (
                  <S.ImageOverlay>
                    <IconWarning color={appTheme.colors.red} scale={0.27} />
                  </S.ImageOverlay>
                )}
                <Image src={getPublicAssetUrl(image)} placeholderScale={0.6} />
              </S.ImageWrapper>

              <S.TitleWrapper data-testid='DetailsRecording-Title'>
                <Text $sizeXLarge $secondary>
                  {title}
                </Text>
                {Boolean(prLevel) && <ParentalControlIcon prLevel={prLevel!} />}
                {accessibilityIconsRender()}
              </S.TitleWrapper>
              {isRecording ? (
                <S.ButtonsWrapper>
                  <PrimaryButton
                    variant='orange'
                    type='button'
                    onClick={handleDeleteButton}
                  >
                    <Text $primary>
                      {formatMessage(messages.cancelRecording)}
                    </Text>
                  </PrimaryButton>
                </S.ButtonsWrapper>
              ) : (
                <S.ButtonsWrapper>
                  {shouldShowPlayButton && (
                    <PrimaryButton
                      variant='orange'
                      type='button'
                      onClick={onPlayClick}
                    >
                      <Text $primary>{formatMessage(messages.playButton)}</Text>
                    </PrimaryButton>
                  )}
                  <PrimaryButton
                    variant='default'
                    type='button'
                    onClick={handleDeleteButton}
                  >
                    <Text $primary>{formatMessage(messages.deleteButton)}</Text>
                  </PrimaryButton>
                </S.ButtonsWrapper>
              )}
            </S.TopGrid>
            {isFailed && (
              <Text data-testid='DetailsRecording-FailedRecordingCaption' $red>
                {formatMessage(messages.failedRecording)}
              </Text>
            )}
            <Text data-testid='DetailsRecording-Description'>
              {description}
            </Text>
            {isNpvrForRecordingChannelDisabled && (
              <Text
                data-testid='DetailsRecording-RecordingNotAvailableOnOtg'
                $highlight
                $sizeSmall
              >
                {formatMessage(messages.recordingNotAvailableOnOtg)}
              </Text>
            )}
            <S.ShortInfoContainer data-testid='DetailsRecording-ShortInfoContainer'>
              {renderShortInfo()}
            </S.ShortInfoContainer>
          </S.RecordingDetailsWrapper>
        </>
      )}
    </>
  );
};
