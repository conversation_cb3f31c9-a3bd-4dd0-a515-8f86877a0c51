import styled, { css } from 'styled-components';

export const RecordingDetailsWrapper = styled.div`
  display: flex;
  flex-direction: column;
  row-gap: 2.4rem;
  height: 100%;
  overflow-y: auto;
`;

export const TopGrid = styled.div`
  display: grid;
  grid-template-columns: 20rem 1fr;
  grid-template-rows: 1fr 1fr;
  column-gap: 4.8rem;
`;

export const ImageWrapper = styled.div`
  width: 20rem;
  grid-row: 1 / 3;
  grid-column: 1 / 2;
  position: relative;
`;

export const TitleWrapper = styled.div`
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.8rem;
`;

export const ButtonsWrapper = styled.div`
  display: flex;
  grid-row: 2 / 3;
  grid-column: 2/3;
  column-gap: 2.4rem;
`;

export const ShortInfoContainer = styled.div`
  display: flex;
  flex-direction: column;
`;

export const ImageOverlay = styled.div`
  position: absolute;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
`;
