import { useIntl } from 'react-intl';

import { PosterPreviewSlider } from 'components/PosterPreviewSlider';
import { PosterPreview } from 'components/PosterPreview';
import { getPublicAssetUrl } from 'utils/url';
import { DetailsViewType } from 'services/detailsView';
import { useAppLayoutMode } from 'services/appLayoutMode';
import { VodPosterPreview } from 'components/VodPosterPreview';
import { SearchBox } from 'components/SearchBox';
import { Text } from 'components/Typography';
import { ChannelsGrid } from 'components/ChannelsGrid';
import { SearchResult } from 'services/api/newApi/optional/search';

import * as S from './styles';
import { useSearch, useSearchOrder } from './hooks';
import { messages } from './messages';
import { CombinedSearchResult, SearchOrder } from './types';
import { areResultsNotEmpty } from './helpers';

export const Search = () => {
  const { formatMessage } = useIntl();
  const { setIsSearchOpen } = useAppLayoutMode();

  const {
    searchValue,
    setSearchValue,
    searchResults,
    isFetching,
    handleOpenDetails,
    subscribedChannels,
  } = useSearch(() => {
    setIsSearchOpen(false);
  });
  const { searchOrder } = useSearchOrder();

  const selectBestEpisode = (liveProgram: SearchResult) => {
    let programId = liveProgram.extId;
    let channelExtId: string | undefined;

    if (liveProgram.epgNearestCommonNamePrograms?.length) {
      const subscribedProgram = liveProgram.epgNearestCommonNamePrograms.find(
        (program: any) =>
          subscribedChannels.some(
            (channel) => channel.channelExtId === program.channelExtId,
          ),
      );

      if (subscribedProgram) {
        programId = subscribedProgram.extId;
        channelExtId = subscribedProgram.channelExtId;
      } else {
        programId = liveProgram.epgNearestCommonNamePrograms[0].extId;
        channelExtId = liveProgram.epgNearestCommonNamePrograms[0].channelExtId;
      }
    } else if (liveProgram.epgSeriesNearestEpisodes?.length) {
      const subscribedEpisode = liveProgram.epgSeriesNearestEpisodes.find(
        (episode: any) =>
          subscribedChannels.some(
            (channel) => channel.channelExtId === episode.channelExtId,
          ),
      );

      if (subscribedEpisode) {
        programId = subscribedEpisode.extId;
        channelExtId = subscribedEpisode.channelExtId;
      } else {
        programId = liveProgram.epgSeriesNearestEpisodes[0].extId;
        channelExtId = liveProgram.epgSeriesNearestEpisodes[0].channelExtId;
      }
    }

    return { programId, channelExtId };
  };

  const renderChannels = () =>
    searchResults &&
    areResultsNotEmpty(searchResults, 'channels') && (
      <S.SliderWrapper>
        <ChannelsGrid
          channels={searchResults.channels}
          isSlider={true}
          amountLines={1}
          title={messages.channelSlider.defaultMessage}
        />
      </S.SliderWrapper>
    );

  const renderLives = () =>
    searchResults &&
    areResultsNotEmpty(searchResults, 'lives') && (
      <S.SliderWrapper>
        <PosterPreviewSlider
          title={messages.liveSlider}
          isFetching={isFetching}
        >
          {searchResults.lives?.map((liveProgram) => {
            const { programId, channelExtId } = selectBestEpisode(liveProgram);

            return (
              <S.LivePosterContainer
                key={liveProgram.extId}
                onClick={() =>
                  handleOpenDetails(
                    DetailsViewType.Program,
                    programId,
                    channelExtId,
                  )
                }
                data-testid='Search-LivePosterContainer'
              >
                <PosterPreview
                  title={liveProgram.mainName}
                  src={
                    liveProgram.image
                      ? getPublicAssetUrl(liveProgram.image)
                      : ''
                  }
                />
              </S.LivePosterContainer>
            );
          })}
        </PosterPreviewSlider>
      </S.SliderWrapper>
    );

  const renderVods = () =>
    searchResults &&
    areResultsNotEmpty(searchResults, 'vods') && (
      <S.SliderWrapper>
        <PosterPreviewSlider title={messages.vodSlider} isFetching={isFetching}>
          {searchResults.vods?.map((vod) => (
            <div key={vod.extId}>
              <S.VodPosterContainer
                onClick={() => {
                  handleOpenDetails(DetailsViewType.VOD, vod.extId);
                }}
                data-testid='Search-VodPosterContainer'
              >
                <VodPosterPreview
                  title={vod.mainName}
                  src={vod.image ? getPublicAssetUrl(vod.image) : ''}
                  id={vod.extId}
                />
              </S.VodPosterContainer>
            </div>
          ))}
        </PosterPreviewSlider>
      </S.SliderWrapper>
    );

  const renderActors = () =>
    searchResults &&
    areResultsNotEmpty(searchResults, 'actors') && (
      <S.SliderWrapper>
        <PosterPreviewSlider
          title={messages.personSlider}
          isFetching={isFetching}
        >
          {searchResults.actors?.map((actor, index) => (
            <div key={actor.extId || `actor-${index}`}>
              <S.ActorPosterContainer onClick={() => {}}>
                <PosterPreview
                  src={actor.image ? getPublicAssetUrl(actor.image) : ''}
                  placeholderType='actor'
                  title={actor.mainName}
                />
              </S.ActorPosterContainer>
            </div>
          ))}
        </PosterPreviewSlider>
      </S.SliderWrapper>
    );

  const renderResultsInProperOrder = {
    [SearchOrder.Default]: (
      <>
        {renderChannels()}
        {renderLives()}
        {renderVods()}
        {renderActors()}
      </>
    ),
    [SearchOrder.ProgramsOrder]: (
      <>
        {renderLives()}
        {renderChannels()}
        {renderVods()}
        {renderActors()}
      </>
    ),
    [SearchOrder.ChannelsOrder]: (
      <>
        {renderChannels()}
        {renderLives()}
        {renderVods()}
        {renderActors()}
      </>
    ),
    [SearchOrder.VodsOrder]: (
      <>
        {renderVods()}
        {renderLives()}
        {renderChannels()}
        {renderActors()}
      </>
    ),
  };

  const hasAnyResults = (results: CombinedSearchResult | null) => {
    if (!results) return false;
    return (
      areResultsNotEmpty(results, 'channels') ||
      areResultsNotEmpty(results, 'lives') ||
      areResultsNotEmpty(results, 'vods') ||
      areResultsNotEmpty(results, 'actors')
    );
  };

  return (
    <S.Container key='mainSearchContainer'>
      <SearchBox
        searchValue={searchValue}
        setSearchValue={setSearchValue}
        isOpen={true}
        toggleIsOpen={() => setIsSearchOpen(false)}
      />
      <S.ResultsContainer>
        {searchValue &&
          !isFetching &&
          (hasAnyResults(searchResults) ? (
            renderResultsInProperOrder[searchOrder]
          ) : (
            <S.NoResultsContainer>
              <Text>{formatMessage(messages.noResults)}</Text>
            </S.NoResultsContainer>
          ))}
      </S.ResultsContainer>
    </S.Container>
  );
};
