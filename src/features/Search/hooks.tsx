import { useCallback, useEffect, useMemo, useState } from 'react';

import useDebounce from 'hooks/useDebounce';
import { DetailsViewType, useDetailsView } from 'services/detailsView';
import { usePlayContent } from 'features/Player/Hook';
import { globalConfig } from 'services/config/config';
import { useRegionalTvContext } from 'services/regionalTv';
import {
  Channel,
  useChannelsAllQuery,
} from 'services/api/newApi/live/channels';
import { PlayerMode } from 'features/Player';
import { routes } from 'routes/routes-map';
import { useAppLayoutMode } from 'services/appLayoutMode';
import { useRouteOrigin } from 'containers/Navigation/context';
import {
  SearchResult,
  useSearchQuery,
} from 'services/api/newApi/optional/search';

import { CombinedSearchResult, SearchedChannel, SearchOrder } from './types';
import { DEBOUNCE_TIME, MAX_CATEGORY_RESULTS } from './constants';

export const useSearch = (closeSearch: () => void) => {
  const { deviceType } = globalConfig.terminal;
  const [searchValue, setSearchValue] = useState('');

  const debouncedSearchValue = useDebounce(searchValue, DEBOUNCE_TIME);

  const { data: channelsData = [] } = useChannelsAllQuery();
  const { playChannel } = usePlayContent();

  const subscribedChannels = useMemo(
    () => channelsData.filter((channel) => channel.isSubscribed),
    [channelsData],
  );

  const { data: results = [], isFetching } = useSearchQuery(
    {
      phrase: debouncedSearchValue,
      deviceCat: deviceType,
      hitsSize: MAX_CATEGORY_RESULTS,
    },
    channelsData,
  );

  const { setData: setDataDetailsView } = useDetailsView();
  const { showRegionalTvSelect, isRegionNotSelectedBefore } =
    useRegionalTvContext();

  const handleChannelClicked = (channelId: string) => {
    closeSearch();
    const channelClicked = channelsData.find(
      (channel) => channel.channelExtId === channelId,
    );
    if (channelClicked?.isRegionalTv && isRegionNotSelectedBefore) {
      return showRegionalTvSelect(() =>
        playChannel({ channelExtId: channelId }),
      );
    }
    return playChannel({ channelExtId: channelId });
  };

  const getChannelWithDetails = useCallback(
    (searchResult: SearchResult[], channels: Channel[]) => {
      const searchedChannels = searchResult?.filter(
        (record) => record.type === 'channel',
      );

      const combinedChannels = searchedChannels.flatMap((searchedChannel) => {
        const detailsChannelArray = channels.filter(
          (channel) => channel.channelExtId === searchedChannel.extId,
        );

        return detailsChannelArray.map((detailsChannel) => {
          return { ...searchedChannel, ...detailsChannel } as SearchedChannel;
        });
      });

      return combinedChannels;
    },
    [],
  );

  const searchResults = useMemo((): CombinedSearchResult | null => {
    if (results) {
      return {
        lives:
          results?.filter((record) =>
            ['epgProgram', 'epgCommonPrograms', 'epgSeries'].includes(
              record.type,
            ),
          ) || [],
        vods:
          results
            ?.filter((record) => record.type === 'vod')
            .filter((vod) => vod.vodSupportedDevices?.includes(deviceType)) ||
          [],
        actors:
          results
            ?.filter((record) => record.type === 'person')
            .sort((a, b) => b.hitScore - a.hitScore) || [],
        channels: getChannelWithDetails(results, channelsData),
      };
    }
    return null;
  }, [channelsData, deviceType, getChannelWithDetails, results]);

  const handleOpenDetails = (
    detailsViewType: DetailsViewType,
    id: string,
    channelExtId?: string,
  ) => {
    const programChannel = channelsData
      .filter((channel) => channel.isSubscribed)
      .find((channel) => channel.channelExtId === channelExtId);

    setDataDetailsView({
      type: detailsViewType,
      id,
      isSubscribedChannel: programChannel?.isSubscribed,
      isChannelRecordingAllowed: programChannel?.isRecordingAllowed,
    });
  };

  useEffect(() => {
    setSearchValue(debouncedSearchValue);
  }, [debouncedSearchValue, setSearchValue]);

  return {
    searchValue,
    setSearchValue,
    searchResults,
    isFetching,
    handleChannelClicked,
    handleOpenDetails,
    subscribedChannels,
  };
};

export const useSearchOrder = () => {
  const { originRoute, playerRoute } = useRouteOrigin();

  const [searchOrder, setSearchOrder] = useState<SearchOrder>(
    SearchOrder.Default,
  );

  const { mode } = useAppLayoutMode();

  const handleOrderBasedOnLocation = useCallback((location: string) => {
    switch (location) {
      case routes.channels:
        setSearchOrder(SearchOrder.ChannelsOrder);
        break;
      case routes.program:
        setSearchOrder(SearchOrder.ProgramsOrder);
        break;
      case routes.vod:
        setSearchOrder(SearchOrder.VodsOrder);
        break;
      default:
        setSearchOrder(SearchOrder.Default);
        break;
    }
  }, []);

  useEffect(() => {
    if (
      (mode === PlayerMode.FullScreen || mode === PlayerMode.Expanded) &&
      playerRoute
    ) {
      return handleOrderBasedOnLocation(playerRoute);
    }
    return handleOrderBasedOnLocation(originRoute);
  }, [handleOrderBasedOnLocation, mode, originRoute, playerRoute]);

  return { searchOrder };
};
