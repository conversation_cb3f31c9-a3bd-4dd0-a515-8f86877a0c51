import { VodQualities } from '../types';

export const messages = {
  trailer: {
    id: 'detailsVOD.trailer',
    defaultMessage: 'Zobac<PERSON> zwiastun',
  },
  buy: {
    id: 'detailsVOD.buy',
    defaultMessage: 'Zamów za {price} zł',
  },
  watch: {
    id: 'detailsVOD.watch',
    defaultMessage: 'Oglądaj',
  },
  actors: {
    id: 'detailsVOD.actors',
    defaultMessage: 'Występują',
  },
  vodNotAvailable: {
    id: 'detailsVOD.vodNotAvailable',
    defaultMessage: 'Materiał niedostępny dla tej kategorii urządzeń',
  },
  priceWithoutDiscount: {
    id: 'detailsVOD.priceWithoutDiscount',
    defaultMessage: `{price} zł`,
  },
  vodQualities: {
    [VodQualities.purchase4KQuality]: {
      id: 'detailsVOD.purchase4KQuality',
      defaultMessage: 'Najwy<PERSON><PERSON><PERSON> jako<PERSON> (4K) za {price} zł',
    },
    [VodQualities.purchaseHDQuality]: {
      id: 'detailsVOD.purchaseHDQuality',
      defaultMessage: '<PERSON><PERSON><PERSON> j<PERSON> (HD) za {price} zł',
    },
    [VodQualities.purchaseSDQuality]: {
      id: 'detailsVOD.purchaseSDQuality',
      defaultMessage: 'Standardowa jakość (SD) za {price} zł',
    },
  },
  purchaseDisabledTitle: {
    id: 'detailsVOD.purchaseDisabledTitle',
    defaultMessage: `Zakup niedostępny`,
  },
};
