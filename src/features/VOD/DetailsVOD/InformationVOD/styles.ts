import styled from 'styled-components';
import { motion } from 'framer-motion';

import { H2, Text } from 'components/Typography';
import {
  convertHexToRgbaString,
  RenderLayer,
  setSliderRecordingCardSizesVertical,
} from 'theme';

export const Container = styled(motion.div)<{ $backgroundImage?: string }>`
  position: fixed;
  background-color: ${({ theme }) =>
    convertHexToRgbaString(theme.colors.black, 0.75)};
  background-image: url(${({ $backgroundImage }) => $backgroundImage});
  background-size: cover;
  background-repeat: no-repeat;
  background-position: 20% center;
  height: 90vh;
  width: 100vw;
  top: 10vh;
  left: 0;

  ${RenderLayer('appOverlay')};
`;

export const FavoriteStarContainer = styled.div`
  position: relative;
  width: 42px;
  height: 42px;
  background-color: transparent !important;
`;

export const Background = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  height: 90vh;
  width: 100%;
  background: ${({ theme }) => theme.colors.codGray};
  background: linear-gradient(
    90deg,
    ${({ theme }) => theme.colors.codGray} 0%,
    ${({ theme }) => theme.colors.codGray} 40%,
    transparent 100%
  );

  ${RenderLayer('background')};
`;

export const TitleContainer = styled.div`
  display: flex;
  align-items: center;
  column-gap: 1.6rem;
`;
export const TitleStyled = styled(H2)`
  display: flex;
  align-items: center;
`;

export const DetailsWrapper = styled.div`
  height: 100%;
  overflow-y: auto;
`;

export const DarkText = styled(Text)`
  display: block;
  color: ${({ theme }) => theme.colors.white75};
  margin-bottom: 0.4rem;
`;

export const ButtonWrapper = styled.div`
  display: flex;
  margin-top: 2.4rem;
  margin-bottom: 3.2rem;
  align-items: center;
  gap: 1.6rem;
  flex-wrap: wrap;

  & > :first-child {
    background-color: ${({ theme }) => theme.colors.primary};
  }
`;

export const Desc = styled(Text)`
  display: block;
  color: ${({ theme }) => theme.colors.white50};
  margin-bottom: 2.4rem;
  width: 60%;
`;

export const ActorContainer = styled.div`
  margin-top: 3.2rem;
`;

export const PosterContainer = styled.div`
  flex-grow: 0;
  flex-shrink: 0;
  margin-right: 1.6rem;

  ${setSliderRecordingCardSizesVertical('1.6rem')}
`;

export const LoaderWrapper = styled.div`
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const GenresContainer = styled.div`
  display: flex;
  align-items: center;
`;

export const PriceBeforeDiscount = styled(Text)`
  text-decoration: line-through;
`;

export const NotAvailableTextContainer = styled.div`
  margin: 0.8rem 0;
  display: flex;
  align-items: center;
  column-gap: 0.8rem;
`;
