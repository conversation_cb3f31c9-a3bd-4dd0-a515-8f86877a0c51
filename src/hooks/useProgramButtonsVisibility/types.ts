import { PlayFeatures } from 'services/api/newApi/live/channels';

export type UseProgramButtonsVisibilityProps = {
  programId: string;
  isSubscribedChannel: boolean | undefined;
  startDate: number;
  endDate: number;
  catchupDuration: number | undefined;
  isCatchupDisabled: boolean | undefined;
  playFeatures: PlayFeatures | undefined;
  isStartoverDisabled: boolean | undefined;
  isRecordingAllowed: boolean | undefined;
};
