import { NumberAsString } from 'utils/types';

export const DisabledParentalControl = -1;
export type ParentControlLevelNumber =
  | 0
  | 1
  | 2
  | 3
  | 4
  | 5
  | typeof DisabledParentalControl;
export type ParentControlLevel = NumberAsString<ParentControlLevelNumber>;

export type AssetType = 'tvod' | 'svod' | 'pvod';
export type pinOnPurchase = 'unset' | 'required' | 'disabled';

export type NpvrService = 'npvr';
export type CatchupService = 'catchup';
export type StartOverService = 'stov';

export type TimeShiftingService =
  | NpvrService
  | CatchupService
  | StartOverService;

export type TimeInSeconds = number;
export type TimeInMilliseconds = number;
export type Timestamp = number;

export type VideoId = string;
export type RecordingId = string;
export type ChannelId = string;
export type Hhtech = string;
export type ProgramId = string;
export type SeriesId = string;
export type AssetId = string;
export type VersionId = string;
export type ImagePath = string;

export type ImagePaths = {
  minih: ImagePath;
  miniv: ImagePath;
  small: ImagePath;
  standard: ImagePath;
  thumb: ImagePath;
};

export interface BaseProperties {
  audioDescription?: boolean;
  hohSubtitles?: boolean;
  live?: boolean;
  signLanguage?: boolean;
  subtitles?: boolean;
  tveStreamDisabled?: boolean;
}
