import {
  BaseProperties,
  ChannelId,
  ImagePath,
  ImagePaths,
  ParentControlLevel,
  ProgramId,
  SeriesId,
  Timestamp,
} from 'services/api/common/types';

export interface EpgRequest {
  date: number;
}

export interface ProgramProperties extends BaseProperties {
  catchUpDisabled?: boolean;
  recordingDisabled?: boolean;
  startOverDisabled?: boolean;
}

export interface ScheduleProgram {
  ageRate: string;
  endTimeUtc: Timestamp;
  episodeNumber: string;
  image: ImagePath;
  mediaId: string;
  name: string;
  prLevel: ParentControlLevel;
  programExtId: string;
  properties?: ProgramProperties;
  seriesId: SeriesId;
  startTimeUtc: Timestamp;
}

export interface ChannelSchedule {
  channelExtId: ChannelId;
  name: string;
  programs: ScheduleProgram[];
}

export interface EpgResponse {
  guide: ChannelSchedule[];
  imagePaths?: ImagePaths;
}

export interface CastMember {
  id: string;
  image?: ImagePath;
  name: string;
  role: string;
}

export interface SeriesDetails {
  cycleTitle: string;
  episodeDescription: string;
  episodeNumber: string;
  episodeOriginalTitle: string;
  episodeTitle: string;
  seasonNumber: string;
}

export interface ImagePathsDetails {
  minih: ImagePath;
  miniv: ImagePath;
  small: ImagePath;
  standard: ImagePath;
  thumb: ImagePath;
}

export interface ProgramDetails {
  ageRate: string;
  cast: CastMember[];
  channelExtId: ChannelId;
  channelName: string;
  countries: string[];
  description: string;
  endTimeUtc: Timestamp;
  genre: string;
  image: ImagePath;
  mediaId: string;
  name: string;
  originalName: string;
  prLevel: ParentControlLevel;
  programExtId: ProgramId;
  properties: ProgramProperties;
  series: SeriesDetails;
  seriesId: SeriesId;
  startTimeUtc: Timestamp;
  subgenre: string;
  year: string;
  additionalProperties: string;
  imagePaths?: ImagePathsDetails;
}

export interface SeriesProgram {
  ageRate: string;
  cast: CastMember[];
  channelExtId: ChannelId;
  channelName: string;
  countries: string[];
  description: string;
  endTimeUtc: Timestamp;
  genre: string;
  image: ImagePath;
  mediaId: string;
  name: string;
  originalName: string;
  prLevel: ParentControlLevel;
  programExtId: ProgramId;
  properties: ProgramProperties;
  series: SeriesDetails;
  seriesId: SeriesId;
  startTimeUtc: Timestamp;
  subgenre: string;
  year: string;
}

export interface SeriesResponse {
  seriesId: SeriesId;
  imagePaths?: ImagePathsDetails;
  series: SeriesProgram[];
}

export interface MediaProperties {
  audioDescription?: boolean;
  catchUpDisabled?: boolean;
  hohSubtitles?: boolean;
  live?: boolean;
  recordingDisabled?: boolean;
  signLanguage?: boolean;
  startOverDisabled?: boolean;
  subtitles?: boolean;
  tveStreamDisabled?: boolean;
}

export interface MediaSeriesDetails {
  cycleTitle: string;
  episodeDescription: string;
  episodeNumber: string;
  episodeOriginalTitle: string;
  episodeTitle: string;
  seasonNumber: string;
}

export interface MediaCastMember {
  id: string;
  image: ImagePath;
  name: string;
  role: string;
}

export interface MediaProgram {
  ageRate: string;
  cast: MediaCastMember[];
  channelExtId: ChannelId;
  channelName: string;
  countries: string[];
  description: string;
  endTimeUtc: Timestamp;
  genre: string;
  image: ImagePath;
  mediaId: string;
  name: string;
  originalName: string;
  prLevel: ParentControlLevel;
  programExtId: ProgramId;
  properties: MediaProperties;
  series: MediaSeriesDetails;
  seriesId: SeriesId;
  startTimeUtc: Timestamp;
  subgenre: string;
  year: string;
}

export interface MediaResponse {
  imagePaths: {
    minih: ImagePath;
    miniv: ImagePath;
    small: ImagePath;
    standard: ImagePath;
    thumb: ImagePath;
  };
  media: MediaProgram[];
}
