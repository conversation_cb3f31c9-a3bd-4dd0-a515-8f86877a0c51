import {
  BaseProperties,
  ImagePaths,
  ParentControlLevel,
} from 'services/api/common/types';

export enum RecordingStatus {
  SCHEDULED = 'scheduled',
  RECORDED = 'recorded',
  IN_PROGRESS = 'inProgress',
  FAILED = 'failed',
  DELETED = 'deleted',
}
export interface RecordingsRequest {
  recordingStatus: RecordingStatus;
}
export interface RecordingDetails {
  awards?: Array<string>;
  cast?: Array<CastMember>;
  channelExtId?: string;
  countries?: Array<string>;
  description?: string;
  duration?: number;
  endDate?: number;
  endDateEpg?: number;
  episodeName?: string;
  episodeNumber?: string;
  expirationDate?: number;
  genre?: string;
  image?: string;
  imagePaths?: ImagePaths;
  name?: string;
  originalEpisodeName?: string;
  originalName?: string;
  prLevel?: ParentControlLevel;
  programExtId?: string;
  properties?: RecordingProperties;
  recordingExtId?: string;
  recordingSeason?: string;
  recordingSeriesId?: string;
  startDate?: number;
  startDateEpg?: number;
  status?: RecordingStatus;
  subGenre?: string;
  year?: string;
}

interface CastMember {
  image?: string;
  name?: string;
  role?:
    | 'actor'
    | 'director'
    | 'musicComposer'
    | 'producer'
    | 'host'
    | 'scriptWriter'
    | 'jury'
    | 'guest';
}

interface RecordingProperties extends BaseProperties {}

export interface OrderRecordingResponse {
  recordingExtId?: string;
}

export interface RecordingListResponse {
  imagePaths?: ImagePaths;
  recordingList?: Array<Recording>;
  seriesList?: Array<Series>;
}

export interface Recording {
  channelExtId?: string;
  duration?: number;
  endDate?: number;
  endDateEpg?: number;
  episodeName?: string;
  expirationDate?: number;
  genre?: string;
  image?: string;
  name?: string;
  prLevel?: ParentControlLevel;
  programExtId?: string;
  recordingExtId?: string;
  startDate?: number;
  startDateEpg?: number;
  status?: RecordingStatus;
  episodeNumber: string;
}

export interface Series {
  channelExtId?: string;
  image?: string;
  newestRecording?: Recording;
  oldestRecording?: Recording;
  recordingSeason?: string;
  recordingSeriesId?: string;
  seriesName?: string;
}

export interface RecordingDeleteParams {
  recordingExtId: string;
  status: RecordingStatus;
}

export interface SeriesDeleteParams {
  recordingExtId: string;
  status: RecordingStatus;
}

export interface RecordingDetailsParams {
  recordingExtId: string;
}

export interface RecordingSeriesParams {
  recordingStatus: RecordingStatus;
  recordingSeriesId: string;
  channelExtId: string;
  recordingSeason: string;
}

export interface RecordingSeriesResponse {
  channelExtId?: string;
  description: string;
  image: string;
  imagePaths?: ImagePaths;
  recordings: Array<Recording>;
  recordingSeason: string;
  recordingSeriesId: string;
  seriesName: string;
}

export interface RecordingOrderRequest {
  channelExtId: string;
  programExtId: string;
}

export interface RecordingOrderResponse {
  recordingExtId?: string;
}

export interface SeriesOrderRequest {
  channelExtId: string;
  programExtId: string;
}

export interface RecordingsByChannelParams {
  channelExtId: string;
}

export interface RecordingsByChannelResponse {
  recordingLiteList: Array<LiteRecording>;
}

export interface LiteRecording {
  channelExtId?: string;
  endDate?: number;
  endDateEpg?: number;
  programExtId?: string;
  recordingExtId?: string;
  recordingSeason?: string;
  recordingSeriesId?: string; // If not empty recording is in series recording mode
  startDate?: number;
  startDateEpg?: number;
}

export interface RecordingsByDateParams {
  startDate: string;
  endDate: string;
}

export interface RecordingsByDateResponse {
  recordingLiteList: Array<LiteRecording>;
}
