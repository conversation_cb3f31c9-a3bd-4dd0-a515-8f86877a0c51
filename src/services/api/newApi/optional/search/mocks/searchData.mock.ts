import { createNewApiMock } from 'services/api/mock/mock.generator';

import mockJ<PERSON><PERSON> from './searchData.mock.json';

import { SearchResponse } from '../types';
import { ENDPOINTS } from '../endpoints';

export const searchDataResponseJSON = mockJSON as SearchResponse;

export const searchDataMockHandlers = createNewApiMock<SearchResponse>(
  'get',
  ENDPOINTS.GET_SEARCH_DATA.url,
  searchDataResponseJSON,
);

export const searchDataMockHandlersWithSpy = (spy: jest.Mock) =>
  createNewApiMock<SearchResponse>(
    'get',
    ENDPOINTS.GET_SEARCH_DATA.url,
    searchDataResponseJSON,
    spy,
  );
