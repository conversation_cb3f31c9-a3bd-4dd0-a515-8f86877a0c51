import {
  ChannelId,
  ImagePath,
  ImagePaths,
  ParentControlLevel,
  Timestamp,
} from 'services/api/common/types';
import { Channel } from 'services/api/newApi/live/channels';

export type SearchType =
  | 'live'
  | 'vod'
  | 'actor'
  | 'director'
  | 'title'
  | 'description'
  | 'channelName';

export type SearchResultType =
  | 'vod'
  | 'channel'
  | 'person'
  | 'epgProgram'
  | 'epgCommonPrograms'
  | 'epgSeries';

export interface EpgNearestCommonNameProgram {
  extId: string;
  image: ImagePath;
  channelExtId: ChannelId;
  startTimeUTC: Timestamp;
  endTimeUTC: Timestamp;
}

export interface EpgSeriesNearestEpisode {
  extId: string;
  channelExtId: ChannelId;
  seasonTitle: string;
  episodeImage: ImagePath;
  startTimeUTC: Timestamp;
  endTimeUTC: Timestamp;
}

export interface EpgNearestBroadcast {
  extId: string;
  channelExtId: ChannelId;
}

export interface PersonInEpgMovie {
  epgNearestBroadcasts?: EpgNearestBroadcast[];
  movieName?: string;
  movieImage?: ImagePath;
  role?: string;
}

export interface PersonInVodMovie {
  movieImage?: ImagePath;
  movieName?: string;
  role?: string;
  supportedDevices?: string[];
  vodAssetExtId?: string;
}

export interface SearchResult {
  hitScore: number;
  type: SearchResultType;
  extId: string;
  mainName: string;
  image?: ImagePath;
  originalTitle?: string;
  prLevel?: ParentControlLevel;
  vodSupportedDevices?: string[];
  epgNearestCommonNamePrograms?: EpgNearestCommonNameProgram[];
  epgSeriesNearestEpisodes?: EpgSeriesNearestEpisode[];
  personInEpgMovies?: PersonInEpgMovie[];
  personInVodMovies?: PersonInVodMovie[];
}

export interface SearchRequest {
  phrase: string;
  deviceCat: string;
  hitsSize?: number;
}

export interface SearchResponse {
  results: SearchResult[];
  imagePaths: {
    epg: ImagePaths;
    person: ImagePaths;
    channel: ImagePaths;
    vod: ImagePaths;
  };
}

export type SearchedChannel = SearchResult & Channel;
