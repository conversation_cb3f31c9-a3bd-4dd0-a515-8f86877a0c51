import { FC, PropsWithChildren, useCallback } from 'react';

import { IconCloseSmall } from 'components/Icons';
import { DetailsVOD } from 'features/VOD/DetailsVOD/DetailsVOD';
import { DetailsProgram } from 'features/Program/DetailsProgram';
import { DetailsViewType } from 'services/detailsView/types';
import { DetailsRecording } from 'features/Recording/DetailsRecording';

import * as S from './styles';
import { DetailsViewProps } from './types';

import { initialDataDetailsView } from '../constants';

export const DetailsView: FC<PropsWithChildren<DetailsViewProps>> = ({
  data,
  setData,
  backgroundImage,
  setBackgroundImage,
}) => {
  const variants = {
    open: {
      opacity: 1,
      y: '33.4vh',
    },
    closed: { opacity: 0, y: '100vh' },
  };
  const variantsBackdrop = {
    open: { opacity: 1, y: 0 },
    closed: { opacity: 0, y: '100vh' },
  };
  const renderChild = useCallback(() => {
    switch (data.type) {
      case DetailsViewType.VOD:
        return (
          <DetailsVOD
            setData={setData}
            dataDetailsView={data}
            setBackgroundImage={setBackgroundImage}
          />
        );

      case DetailsViewType.Program:
        return (
          <DetailsProgram
            setData={setData}
            dataDetailsView={data}
            setBackgroundImage={setBackgroundImage}
          />
        );
      case DetailsViewType.Recording:
        return (
          <DetailsRecording
            setData={setData}
            dataDetailsView={data}
            setBackgroundImage={setBackgroundImage}
          />
        );
      default:
        return <></>;
    }
  }, [data, setBackgroundImage, setData]);

  return (
    <>
      <S.Backdrop
        onClick={() => setData(initialDataDetailsView)}
        variants={variantsBackdrop}
        animate={data.type ? 'open' : 'closed'}
      />
      <S.Container
        animate={data.type ? 'open' : 'closed'}
        variants={variants}
        $backgroundImage={backgroundImage}
      >
        <S.Background />
        <S.IconWrapper onClick={() => setData(initialDataDetailsView)}>
          <IconCloseSmall />
        </S.IconWrapper>
        {renderChild()}
      </S.Container>
    </>
  );
};
