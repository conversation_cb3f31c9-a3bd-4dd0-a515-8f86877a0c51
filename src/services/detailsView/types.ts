import { Dispatch, SetStateAction } from 'react';

export enum DetailsViewType {
  Empty,
  VOD,
  Program,
  Recording,
}

export interface DetailsViewData {
  type: DetailsViewType;
  id: string;
  isSubscribedChannel?: boolean;
  isChannelRecordingAllowed?: boolean;
  dateEmission?: string;
  expirationDate?: string;
}

export interface DetailsViewContextValue {
  data: DetailsViewData;
  setData: Dispatch<SetStateAction<DetailsViewData>>;
  backgroundImage: string;
  setBackgroundImage: Dispatch<SetStateAction<string>>;
}
