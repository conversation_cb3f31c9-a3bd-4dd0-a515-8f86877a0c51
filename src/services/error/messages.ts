import { AxiosError } from 'axios';

import {
  isAxiosError,
  isDownForMaintenanceError,
  isNewServiceError,
  isOldServiceError,
  isServerError,
} from './guards';
import { ErrorMap, ErrorType, ErrorWithDetails } from './types';

export const errorButtonDefaultMessage = 'Odśwież stronę';
export const errorSubmitDefaultMessage = 'OK';

export const errorMessagesMap: ErrorMap = {
  UNEXPECTED_ERROR: {
    title: 'Błąd aplikacji',
    message: 'Wyst<PERSON><PERSON>ł błąd aplikacji. Przeładuj stronę i spróbuj ponownie.',
  },
  CONNECTION_FAILURE: {
    title: 'Błąd połączenia',
    message: '<PERSON>rak dostępu do internetu. Przeładuj stronę i spróbuj ponownie.',
  },
  NOT_FOUND: {
    title: 'Błąd serwera',
    message:
      'Nie znaleziono zasobu. Spróbuj zalogować się ponownie (404 NOT_FOUND)',
  },
  TIMEOUT: {
    title: 'Timeout',
    message:
      'Błąd serwera usługi Orange TV Go. Spróbuj ponownie później (410 TIMEOUT)',
  },
  SYSTEM_FAILURE: {
    title: 'Błąd serwera',
    message: 'Błąd serwera usługi Orange TV Go. Spróbuj ponownie później',
  },
  AUTHORIZATION: {
    title: 'Błąd autoryzacji',
    message: 'Podano nieprawidłowy login lub hasło',
  },
  AUTHORIZATION_QUICK_LOGIN: {
    title: 'Błąd autoryzacji',
    message:
      'Wystąpił błąd podczas próby szybkiego logowania. Upewnij się że wpisany kod dostępu jest poprawny. Jeśli błąd nadal występuje, zaloguj się tradycyjnie za pomocą Nazwy Użytkownika Usługi TV oraz Kodu Poufnego Usługi TV.',
  },
  QUICK_LOGIN_INVALID_PIN: {
    title: 'Błąd autoryzacji',
    message: 'Wprowadzony kod dostępu jest niepoprawny, spróbuj ponownie.',
  },
  NO_AUTHORIZATION: {
    title: 'Brak autoryzacji',
    message: 'Aby zobaczyć wybraną sekcję serwisu, wymagane jest logowanie',
  },
  HOUSEHOLD_SUSPENDED_ON_LOGIN: {
    title: 'Błąd',
    message: 'Konto jest zablokowane z powodu windykacji.',
  },
  HOUSEHOLD_SUSPENDED_ON_ACTION: {
    title: 'Zostałeś wylogowany',
    message: 'Konto jest zablokowane z powodu windykacji.',
  },
  SYSTEM_MAINTENANCE_FULL: {
    title: 'Przerwa techniczna',
    message: 'Przerwa techniczna. Spróbuj ponownie później.',
  },
  SYSTEM_MAINTENANCE_READ_ONLY: {
    title: 'Przerwa techniczna',
    message: 'Przerwa techniczna. Spróbuj ponownie później',
  },
  TRAILER_NOT_AVAILABLE: {
    title: 'Zwiastun niedostępny',
    message:
      'Zwiastun dla tego materiału nie jest dostępny. Spróbuj ponownie później',
  },
  UNABLE_TO_REGISTER_TERMINAL: {
    title: 'Limit terminali osiągnięty',
    message:
      'Z powodu zbyt dużej ilości zarejestrowanych terminali nie możesz dostać się do serwisu. Usuń jeden z nich i spróbuj ponownie.',
  },
  UNABLE_TO_DELETE_TERMINAL: {
    title: 'Wyczerpano limit zmian urządzeń.',
    message:
      'W trakcie bieżącego okresu rozliczeniowego dokonano już dwóch zmian urządzeń. Rejestracja nowego urządzenia będzie możliwa w kolejnym okresie rozliczeniowym.',
  },
  PLAYER_ERROR: {
    title: 'Błąd odtwarzania',
    message: 'Wystąpił problem z odtwarzaniem treści. Spróbuj ponownie później',
  },
  PLAYER_CDN_ERROR: {
    title: 'Błąd serwera treści',
  },
  PLAYER_BLACKOUT: {
    title: 'Błąd odtwarzania',
    message:
      'Program nie jest dostępny w Orange TV Go. Możesz go obejrzeć w Telewizji Orange na swoim dekoderze',
  },
  SERVICE_NOT_AVAILABLE_ON_DEVICE: {
    title: 'Błąd odtwarzania',
    message: 'Materiał nie jest dostępny na tym urządzeniu',
  },
  MAX_SIMULTANEUOS_VIEWS: {
    title: 'Błąd odtwarzania',
    message:
      'Program odtwarzany jest teraz na innym urządzeniu bądź zbyt dużo osób ogląda telewizję na tym koncie',
  },
  SOURCE_TYPE_NOT_ALLOWED: {
    title: 'Błąd odtwarzania',
    message: 'Kanał ze źródła DVB-SI, MHP, IP nie jest dozwolony',
  },
  LOGIN_NOT_ALLOWED: {
    title: 'Konto zablokowane',
    message: 'Zweryfikuj dane i spróbuj ponownie za jakiś czas',
  },
  DELETE_FAIL: {
    title: 'Błąd usuwania',
    message: 'Nie udało się usunąć',
  },
  PLAYER_TIME_LIMIT: {
    title: 'Przekroczono limit czasu',
    message: 'Czy nadal oglądasz?',
  },
  DOWN_FOR_MAINTENANCE: {
    title: 'Usługa niedostępna',
    buttonMessage: 'Odśwież',
  },
  BAD_COUNTRY_CODE: {
    title: 'Informacja',
    message:
      'Nie możemy odtworzyć Ci tej treści. Być może łączysz się z kraju, w którym nie możemy udostępniać naszych treści wideo. Jeśli jest inaczej, zmień ustawienia Twojego połączenia, abyśmy mogli sprawdzić, z jakiego kraju się łączysz (np. połącz się bez serwera proxy lub bez VPN). Jeśli nadal zobaczysz ten komunikat, skontaktuj się z nami.',
    buttonMessage: 'OK',
  },
  USER_NOT_SUBSCRIBED_TO_CHANNEL: {
    title: 'Kanał niedostępny',
  },
  INVALID_TICKET: {
    title: 'Wystąpił błąd',
    message: 'Nie masz dostępu do tej treści',
  },
  NO_MATCHING_SOURCE_TYPE: {
    title: 'Wystąpił błąd',
    message: 'Wybrany kanał nie jest dostępny na tym urządzeniu',
  },
  SERVICE_UNSUBSCRIBED: {
    title: 'Usługa niedostępna',
    message: 'Konto nie posiada subskrypcji wymaganej usługi',
  },
  NO_NPVR_AVAILABLE: {
    title: 'Usługa niedostępna',
    message: 'Usługa niedostępna w twoim pakiecie',
  },
  NAME_ALREADY_EXISTS: {
    title: 'Wystąpił błąd',
    message: 'Profil o podanej nazwie już istnieje',
  },
  CHANGE_PARENTAL_PIN_INVALID_PIN: {
    title: 'Błąd',
    message: 'Wprowadzony kod jest nieprawidłowy',
  },
  ALL_PARENTAL_CODE_AUTHORIZATION_ATTEMPTS_USED: {
    title: 'Błąd autoryzacji',
    message:
      'Limit prób autoryzacji kodem dostępu został wyczerpany. Użyj autoryzacji kodem poufnym.',
  },
  ALL_SECRET_CODE_AUTHORIZATION_ATTEMPTS_USED: {
    title: 'Błąd autoryzacji',
    message:
      'Limit prób autoryzacji kodem poufnym został wyczerpany. Użyj autoryzacji kodem dostępu.',
  },
  ALL_AUTHORIZATION_METHODS_USED: {
    title: 'Błąd autoryzacji',
    message:
      'Wykorzystano wszystkie próby autoryzacji, nadanie nowego kodu dostępu do usługi będzie możliwe za 48h.',
  },
  VERIFICATION_LOCKED: {
    title: 'Błąd autoryzacji',
    message:
      'Przekroczono liczbę możliwych prób wpisania kodu. Odblokuj kod dostępu w ustawieniach (ustawienia > zabezpieczenia > zmień kod dostępu).',
  },
};

const HTTPStatusCodeToErrorMessageMap: {
  [code in number]?: keyof typeof errorMessagesMap;
} = {
  401: 'NO_AUTHORIZATION',
  404: 'NOT_FOUND',
  410: 'TIMEOUT',
  503: 'SYSTEM_MAINTENANCE_FULL',
  555: 'SYSTEM_MAINTENANCE_READ_ONLY',
};

export const getErrorMessage = (errorType: ErrorType) => {
  return errorMessagesMap[errorType];
};

export const getErrorMessageByHTTPStatusCode = (statusCode: number) => {
  return HTTPStatusCodeToErrorMessageMap[statusCode];
};

const getErrorMessageIfAxiosError = (error: AxiosError) => {
  if (error.response?.status) {
    return getErrorMessageByHTTPStatusCode(error.response.status);
  }
};

export const getErrorMessageFromError = (error: AxiosError) => {
  if (isOldServiceError(error) && error.response.data.errMsg) {
    return error.response.data.errMsg;
  }

  if (isNewServiceError(error) && error.response.data.errMsg) {
    return error.response.data.errMsg;
  }

  return null;
};

export const getErrorMessageMapElement = (error: unknown) => {
  if (isOldServiceError(error)) {
    return error.response.data.errCode as ErrorType;
  }

  return null;
};

const ICON_ERROR_PAGE = [
  'DOWN_FOR_MAINTENANCE',
  'SYSTEM_MAINTENANCE_FULL',
  'SYSTEM_MAINTENANCE_READ_ONLY',
  'SYSTEM_FAILURE',
];

export const shouldDisplayIconErrorPage = (error: ErrorType) => {
  return ICON_ERROR_PAGE.includes(error);
};

export const getUserErrorType = (
  error: unknown,
): ErrorType | ErrorWithDetails => {
  // Handle unknown errors
  if (!(error instanceof Error)) {
    return 'UNEXPECTED_ERROR';
  }

  // Handle type errors
  // noinspection SuspiciousTypeOfGuard
  if (error instanceof TypeError) {
    return 'UNEXPECTED_ERROR';
  }
  if (isDownForMaintenanceError(error)) {
    return 'DOWN_FOR_MAINTENANCE';
  }

  // Handle any error that is not axios error
  if (!isAxiosError(error)) {
    return 'UNEXPECTED_ERROR';
  }

  // No connection error
  if (!error.response) {
    return 'CONNECTION_FAILURE';
  }

  // Handle HTTP status code errors
  const messageType = getErrorMessageIfAxiosError(error);

  if (messageType) {
    return messageType;
  }

  // Handle HTTP 5xx errors
  if (isServerError(error)) {
    return {
      error: (error.response.data as any).errCode || 'SYSTEM_FAILURE',
      errorStatus: error.response.status,
      errorCode: (error.response.data as any).errCode || 'SYSTEM_FAILURE',
    };
  }

  const messageFromMap = getErrorMessageMapElement(error);

  if (messageFromMap) {
    return messageFromMap;
  }

  // Unhandled case
  return 'UNEXPECTED_ERROR';
};
