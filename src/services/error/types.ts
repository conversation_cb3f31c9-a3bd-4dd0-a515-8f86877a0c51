import React from 'react';

export type ErrorType =
  | 'UNEXPECTED_ERROR'
  | 'CONNECTION_FAILURE'
  | 'NOT_FOUND'
  | 'TIMEOUT'
  | 'SYSTEM_FAILURE'
  | 'SYSTEM_MAINTENANCE_FULL'
  | 'SYSTEM_MAINTENANCE_READ_ONLY'
  | 'AUTHORIZATION'
  | 'AUTHORIZATION_QUICK_LOGIN'
  | 'QUICK_LOGIN_INVALID_PIN'
  | 'NO_AUTHORIZATION'
  | 'TRAILER_NOT_AVAILABLE'
  | 'UNABLE_TO_REGISTER_TERMINAL'
  | 'UNABLE_TO_DELETE_TERMINAL'
  | 'PLAYER_ERROR'
  | 'PLAYER_CDN_ERROR'
  | 'PLAYER_BLACKOUT'
  | 'SERVICE_NOT_AVAILABLE_ON_DEVICE'
  | 'SERVICE_UNSUBSCRIBED'
  | 'NO_NPVR_AVAILABLE'
  | 'MAX_SIMULTANEUOS_VIEWS'
  | 'SOURCE_TYPE_NOT_ALLOWED'
  | 'LOGIN_NOT_ALLOWED'
  | 'DELETE_FAIL'
  | 'DOWN_FOR_MAINTENANCE'
  | 'BAD_COUNTRY_CODE'
  | 'PLAYER_TIME_LIMIT'
  | 'USER_NOT_SUBSCRIBED_TO_CHANNEL'
  | 'INVALID_TICKET'
  | 'NO_MATCHING_SOURCE_TYPE'
  | 'HOUSEHOLD_SUSPENDED_ON_LOGIN'
  | 'HOUSEHOLD_SUSPENDED_ON_ACTION'
  | 'NAME_ALREADY_EXISTS'
  | 'CHANGE_PARENTAL_PIN_INVALID_PIN'
  | 'ALL_PARENTAL_CODE_AUTHORIZATION_ATTEMPTS_USED'
  | 'ALL_SECRET_CODE_AUTHORIZATION_ATTEMPTS_USED'
  | 'ALL_AUTHORIZATION_METHODS_USED'
  | 'VERIFICATION_LOCKED';

export enum PlayerPlayInfoError {
  USER_NOT_SUBSCRIBED_TO_CHANNEL = 'USER_NOT_SUBSCRIBED_TO_CHANNEL',
  MAX_SIMULTANEUOS_VIEWS = 'MAX_SIMULTANEUOS_VIEWS',
  SERVICE_NOT_AVAILABLE_ON_DEVICE = 'SERVICE_NOT_AVAILABLE_ON_DEVICE',
  SERVICE_UNSUBSCRIBED = 'SERVICE_UNSUBSCRIBED',
  NO_NPVR_AVAILABLE = 'NO_NPVR_AVAILABLE',
  INVALID_TICKET = 'INVALID_TICKET',
  NO_MATCHING_SOURCE_TYPE = 'NO_MATCHING_SOURCE_TYPE',
  SOURCE_TYPE_NOT_ALLOWED = 'SOURCE_TYPE_NOT_ALLOWED',
  NOT_REGISTERED = 'NOT_REGISTERED', // Terminal error on live, catchup, recording
  INVALID_TERMINAL = 'INVALID_TERMINAL', // Terminal error on vod
  INVALID_VIDEO_ID = 'INVALID_VIDEO_ID', // Vod specific error
  INVALID_TEMPLATE = 'INVALID_TEMPLATE', // Vod specific error
  INVALID_PARAMETER_VALUE = 'INVALID_PARAMETER_VALUE', // Vod specific error
  UNKNOWN_USER = 'UNKNOWN_USER',
  HOUSEHOLD_SUSPENDED = 'HOUSEHOLD_SUSPENDED',
  BAD_COUNTRY_CODE = 'BAD_COUNTRY_CODE',
}

export enum PlayerPlayInfoErrorCode {
  INVALID_TERMINAL = 'G000408',
  TERMINAL_NOT_REGISTERED = 'G000407',
  MAX_SIMULTANEOUS_VIEWS = 'G000800',
  USER_NOT_SUBSCRIBED_TO_CHANNEL = 'G000502',
  NO_NPVR_AVAILABLE = 'G000503',
  HOUSEHOLD_SUSPENDED = 'G000008',
  SERVICE_UNSUBSCRIBED = 'G000503',
}

export enum ErrorCodeMessages {
  AUTOLOGIN_INVALID_PIN = 'AUTOLOGIN_INVALID_PIN',
}

export interface ErrorMessage {
  title: string;
  message?: string;
  buttonMessage?: string;
}

export type ErrorMap = Record<ErrorType, ErrorMessage>;

export interface APIError {
  errCode: string;
  errMsg: string;
}

export interface NewAPIError {
  errCode: string;
  errMsg: string;
  error: string;
}

export type ErrorWithDetails = {
  error: ErrorType;
  errorCode: string;
  errorStatus: number;
};

export interface ErrorBoundaryProps {}

export type ErrorBoundaryState =
  | {
      hasError: false;
    }
  | {
      hasError: true;
      error: Error;
      errorInfo: React.ErrorInfo;
    };

export enum Layouts {
  ERROR_PAGE = 'ERROR_PAGE',
  ERROR_MODAL = 'ERROR_MODAL',
  NO_ERROR = 'NO_ERROR',
}

export type ErrorScreenState =
  | {
      layout: 'NO_ERROR';
      errorType: '';
    }
  | {
      layout: 'ERROR_PAGE';
      errorType: ErrorType;
    }
  | {
      layout: 'ERROR_MODAL';
      errorType: ErrorType;
    };

export type RenderErrorProps = Record<Layouts, JSX.Element>;

export type ErrorScreenContextValue = {
  showErrorPage(errorType: ErrorType | ErrorWithDetails): void;
  showErrorModal(
    errorType: ErrorType | ErrorWithDetails,
    configMessage?: string,
  ): void;
  clear(): void;
};

export interface ErrorPageProps {
  errorType: ErrorType;
  errorMessage?: string | null;
  errorDetails?: string;
}

export interface ErrorScreenProviderProps {}

export interface ErrorModalProps {
  isOpen: boolean;
  errorType: ErrorType;
  onClose: () => void;
  errorDetails?: string;
  errorMessage?: string;
}
export interface KaboomSyncProps {
  shouldFail?: () => boolean;
  notify?(message: string): void;
}

export interface KaboomAsyncProps {
  kaboomFn: () => Promise<unknown>;
  asModal?: boolean;
}
