import { describe, it, expect } from 'vitest';
import { getAccessibilityFeaturesList } from '../getAccessibilityFeaturesList';
import { AccessibilityFeature } from 'components/AccessibilityIcon';

describe('getAccessibilityFeaturesList', () => {
  it('should return empty array when properties are undefined', () => {
    const result = getAccessibilityFeaturesList(undefined);
    expect(result).toEqual([]);
  });

  it('should return empty array when properties object is empty', () => {
    const result = getAccessibilityFeaturesList({});
    expect(result).toEqual([]);
  });

  it('should include AUDIO_DESCRIPTION when audioDescription is true', () => {
    const properties = { audioDescription: true };
    const result = getAccessibilityFeaturesList(properties);
    expect(result).toContain(AccessibilityFeature.AUDIO_DESCRIPTION);
    expect(result).toHaveLength(1);
  });

  it('should include SIGN_LANGUAGE when signLanguage is true', () => {
    const properties = { signLanguage: true };
    const result = getAccessibilityFeaturesList(properties);
    expect(result).toContain(AccessibilityFeature.SIGN_LANGUAGE);
    expect(result).toHaveLength(1);
  });

  it('should include SUBTITLES_FOR_DEAF when hohSubtitles is true', () => {
    const properties = { hohSubtitles: true };
    const result = getAccessibilityFeaturesList(properties);
    expect(result).toContain(AccessibilityFeature.SUBTITLES_FOR_DEAF);
    expect(result).toHaveLength(1);
  });

  it('should include multiple features when multiple properties are true', () => {
    const properties = {
      audioDescription: true,
      signLanguage: true,
      hohSubtitles: true,
    };
    const result = getAccessibilityFeaturesList(properties);

    expect(result).toContain(AccessibilityFeature.AUDIO_DESCRIPTION);
    expect(result).toContain(AccessibilityFeature.SIGN_LANGUAGE);
    expect(result).toContain(AccessibilityFeature.SUBTITLES_FOR_DEAF);
    expect(result).toHaveLength(3);
  });

  it('should not include features when properties are false', () => {
    const properties = {
      audioDescription: false,
      signLanguage: false,
      hohSubtitles: false,
    };
    const result = getAccessibilityFeaturesList(properties);
    expect(result).toEqual([]);
  });

  it('should handle mixed true and false properties correctly', () => {
    const properties = {
      audioDescription: true,
      signLanguage: false,
      hohSubtitles: true,
    };
    const result = getAccessibilityFeaturesList(properties);

    expect(result).toContain(AccessibilityFeature.AUDIO_DESCRIPTION);
    expect(result).toContain(AccessibilityFeature.SUBTITLES_FOR_DEAF);
    expect(result).not.toContain(AccessibilityFeature.SIGN_LANGUAGE);
    expect(result).toHaveLength(2);
  });

  it('should ignore unrelated properties', () => {
    const properties = {
      audioDescription: true,
      someOtherProperty: true,
      anotherProperty: 'value',
    };
    const result = getAccessibilityFeaturesList(properties);

    expect(result).toContain(AccessibilityFeature.AUDIO_DESCRIPTION);
    expect(result).toHaveLength(1);
  });
});
