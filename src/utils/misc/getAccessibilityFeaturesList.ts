import { AccessibilityFeature } from 'components/AccessibilityIcon';
import { BaseProperties } from 'services/api/common/types';

export const getAccessibilityFeaturesList = (properties?: BaseProperties) => {
  const accessibilityFeatures = [];
  if (properties) {
    properties.audioDescription &&
      accessibilityFeatures.push(AccessibilityFeature.AUDIO_DESCRIPTION);
    properties.signLanguage &&
      accessibilityFeatures.push(AccessibilityFeature.SIGN_LANGUAGE);
    properties.hohSubtitles &&
      accessibilityFeatures.push(AccessibilityFeature.SUBTITLES_FOR_DEAF);
  }
  return accessibilityFeatures;
};
